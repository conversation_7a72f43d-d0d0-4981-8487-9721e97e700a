/**
 * Review API
 * Simple review interface for human approval/rejection
 */

import { NextRequest, NextResponse } from 'next/server';
import { SimplifiedStateStore } from '../../../../core/state/store';
import { SimplifiedReviewSystem } from '../../../../core/review/system';
import { ResponseFormatter } from '../../../../core/api/response-formatter';
import { ErrorType } from '../../../../core/utils/error-handler';

// Initialize system components
const stateStore = new SimplifiedStateStore();
const reviewSystem = new SimplifiedReviewSystem(stateStore);

// Initialize state store
stateStore.initialize().catch(console.error);

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const requestId = ResponseFormatter.getRequestId(request);

  try {
    const { id } = await params;
    const reviewId = id;

    // Get review data
    const reviewData = await reviewSystem.getReview(reviewId);

    return ResponseFormatter.success(reviewData, requestId);

  } catch (error) {
    console.error('Review fetch error:', error);

    if (error instanceof Error && error.message.includes('not found')) {
      return NextResponse.json(
        { error: 'Review not found' },
        { status: 404 }
      );
    }

    if (error instanceof Error && error.message.includes('expired')) {
      return NextResponse.json(
        { error: 'Review has expired' },
        { status: 410 }
      );
    }

    return NextResponse.json(
      {
        error: 'Failed to fetch review',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const reviewId = id;
    const body = await request.json();
    const { decision, edits, reviewer } = body;

    // Debug logging
    console.log(`📝 Review submission for ${reviewId}:`, { decision, edits, reviewer });

    // Validate required fields
    if (!decision || !['approve', 'reject'].includes(decision)) {
      console.log(`❌ Invalid decision: ${decision}`);
      return NextResponse.json(
        { error: 'Valid decision (approve/reject) is required' },
        { status: 400 }
      );
    }

    // Submit review
    await reviewSystem.submitReview(reviewId, decision, edits);

    return NextResponse.json({
      success: true,
      data: {
        reviewId,
        decision,
        submittedAt: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Review submission error:', error);

    if (error instanceof Error && error.message.includes('not found')) {
      return NextResponse.json(
        { error: 'Review not found' },
        { status: 404 }
      );
    }

    if (error instanceof Error && error.message.includes('expired')) {
      return NextResponse.json(
        { error: 'Review has expired' },
        { status: 410 }
      );
    }

    if (error instanceof Error && error.message.includes('already completed')) {
      return NextResponse.json(
        { error: 'Review already completed' },
        { status: 409 }
      );
    }

    return NextResponse.json(
      {
        error: 'Failed to submit review',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}
