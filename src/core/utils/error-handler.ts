/**
 * Centralized Error Handler
 * Standardizes error handling patterns across the workflow system
 */

import { v4 as uuidv4 } from 'uuid';

// Standard Error Types
export enum ErrorType {
  WORKFLOW = 'workflow',
  AI = 'ai',
  REVIEW = 'review',
  SYSTEM = 'system',
  VALIDATION = 'validation',
  NETWORK = 'network',
  AUTHENTICATION = 'authentication',
  AUTHORIZATION = 'authorization'
}

export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

// Standard Error Interface
export interface StandardError {
  id: string;
  type: ErrorType;
  severity: ErrorSeverity;
  message: string;
  code: string;
  context: Record<string, any>;
  timestamp: string;
  correlationId?: string;
  recoverable: boolean;
  retryable: boolean;
  stack?: string;
}

// Error Response Format
export interface ErrorResponse {
  success: false;
  error: {
    type: ErrorType;
    code: string;
    message: string;
    details?: any;
    correlationId: string;
    timestamp: string;
  };
  meta?: {
    requestId?: string;
    version?: string;
  };
}

// Success Response Format
export interface SuccessResponse<T = any> {
  success: true;
  data: T;
  meta?: {
    requestId?: string;
    version?: string;
    pagination?: {
      page: number;
      limit: number;
      total: number;
      hasNext: boolean;
      hasPrev: boolean;
    };
  };
}

export type APIResponse<T = any> = SuccessResponse<T> | ErrorResponse;

// Error Recovery Strategy
export interface ErrorRecoveryStrategy {
  maxRetries: number;
  retryDelayMs: number;
  backoffMultiplier: number;
  retryableErrors: string[];
  fallbackAction?: () => Promise<any>;
}

export class ErrorHandler {
  private static instance: ErrorHandler;
  private correlationId: string;
  private errorLog: StandardError[] = [];

  private constructor() {
    this.correlationId = uuidv4();
  }

  static getInstance(): ErrorHandler {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler();
    }
    return ErrorHandler.instance;
  }

  /**
   * Create a standardized error
   */
  createError(
    type: ErrorType,
    code: string,
    message: string,
    context: Record<string, any> = {},
    severity: ErrorSeverity = ErrorSeverity.MEDIUM
  ): StandardError {
    const error: StandardError = {
      id: uuidv4(),
      type,
      severity,
      message,
      code,
      context,
      timestamp: new Date().toISOString(),
      correlationId: this.correlationId,
      recoverable: this.isRecoverable(type, code),
      retryable: this.isRetryable(type, code),
      stack: new Error().stack
    };

    this.logError(error);
    return error;
  }

  /**
   * Create standardized API error response
   */
  createErrorResponse(
    type: ErrorType,
    code: string,
    message: string,
    details?: any,
    requestId?: string
  ): ErrorResponse {
    return {
      success: false,
      error: {
        type,
        code,
        message,
        details,
        correlationId: this.correlationId,
        timestamp: new Date().toISOString()
      },
      meta: {
        requestId,
        version: '1.0'
      }
    };
  }

  /**
   * Create standardized API success response
   */
  createSuccessResponse<T>(
    data: T,
    requestId?: string,
    pagination?: any
  ): SuccessResponse<T> {
    return {
      success: true,
      data,
      meta: {
        requestId,
        version: '1.0',
        pagination
      }
    };
  }

  /**
   * Handle and transform errors
   */
  handleError(error: any, context: Record<string, any> = {}): StandardError {
    if (error instanceof Error) {
      return this.createError(
        this.inferErrorType(error),
        this.inferErrorCode(error),
        error.message,
        { ...context, originalStack: error.stack },
        this.inferSeverity(error)
      );
    }

    return this.createError(
      ErrorType.SYSTEM,
      'UNKNOWN_ERROR',
      String(error),
      context,
      ErrorSeverity.MEDIUM
    );
  }

  /**
   * Execute with error recovery
   */
  async executeWithRecovery<T>(
    operation: () => Promise<T>,
    strategy: Partial<ErrorRecoveryStrategy> = {},
    context: Record<string, any> = {}
  ): Promise<T> {
    const defaultStrategy: ErrorRecoveryStrategy = {
      maxRetries: 3,
      retryDelayMs: 1000,
      backoffMultiplier: 2,
      retryableErrors: ['NETWORK_ERROR', 'TIMEOUT', 'RATE_LIMIT'],
      ...strategy
    };

    let lastError: StandardError | null = null;

    for (let attempt = 0; attempt <= defaultStrategy.maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = this.handleError(error, { ...context, attempt });

        if (attempt === defaultStrategy.maxRetries || !this.shouldRetry(lastError, defaultStrategy.retryableErrors)) {
          break;
        }

        const delay = defaultStrategy.retryDelayMs * Math.pow(defaultStrategy.backoffMultiplier, attempt);
        await this.sleep(delay);
      }
    }

    // Try fallback if available
    if (defaultStrategy.fallbackAction) {
      try {
        return await defaultStrategy.fallbackAction();
      } catch (fallbackError) {
        this.handleError(fallbackError, { ...context, fallback: true });
      }
    }

    throw lastError;
  }

  private shouldRetry(error: StandardError, retryableErrors: string[]): boolean {
    return error.retryable && retryableErrors.includes(error.code);
  }

  /**
   * Log error to system
   */
  private logError(error: StandardError): void {
    this.errorLog.push(error);
    
    // Keep only last 1000 errors in memory
    if (this.errorLog.length > 1000) {
      this.errorLog = this.errorLog.slice(-1000);
    }

    // Console logging with structured format
    const logLevel = this.getLogLevel(error.severity);
    console[logLevel](`[${error.type.toUpperCase()}] ${error.code}: ${error.message}`, {
      id: error.id,
      correlationId: error.correlationId,
      context: error.context,
      timestamp: error.timestamp
    });
  }

  private isRecoverable(type: ErrorType, code: string): boolean {
    const nonRecoverableErrors = ['VALIDATION_ERROR', 'AUTHENTICATION_FAILED', 'AUTHORIZATION_DENIED'];
    return !nonRecoverableErrors.includes(code);
  }

  private isRetryable(type: ErrorType, code: string): boolean {
    const retryableErrors = ['NETWORK_ERROR', 'TIMEOUT', 'RATE_LIMIT', 'SERVICE_UNAVAILABLE'];
    return retryableErrors.includes(code);
  }

  private inferErrorType(error: Error): ErrorType {
    const message = error.message.toLowerCase();
    if (message.includes('workflow')) return ErrorType.WORKFLOW;
    if (message.includes('ai') || message.includes('model')) return ErrorType.AI;
    if (message.includes('review')) return ErrorType.REVIEW;
    if (message.includes('validation')) return ErrorType.VALIDATION;
    if (message.includes('network') || message.includes('fetch')) return ErrorType.NETWORK;
    if (message.includes('auth')) return ErrorType.AUTHENTICATION;
    return ErrorType.SYSTEM;
  }

  private inferErrorCode(error: Error): string {
    const message = error.message.toLowerCase();
    if (message.includes('not found')) return 'NOT_FOUND';
    if (message.includes('timeout')) return 'TIMEOUT';
    if (message.includes('rate limit')) return 'RATE_LIMIT';
    if (message.includes('validation')) return 'VALIDATION_ERROR';
    if (message.includes('unauthorized')) return 'AUTHENTICATION_FAILED';
    if (message.includes('forbidden')) return 'AUTHORIZATION_DENIED';
    return 'UNKNOWN_ERROR';
  }

  private inferSeverity(error: Error): ErrorSeverity {
    const message = error.message.toLowerCase();
    if (message.includes('critical') || message.includes('fatal')) return ErrorSeverity.CRITICAL;
    if (message.includes('warning') || message.includes('timeout')) return ErrorSeverity.MEDIUM;
    return ErrorSeverity.HIGH;
  }

  private getLogLevel(severity: ErrorSeverity): 'error' | 'warn' | 'info' {
    switch (severity) {
      case ErrorSeverity.CRITICAL:
      case ErrorSeverity.HIGH:
        return 'error';
      case ErrorSeverity.MEDIUM:
        return 'warn';
      case ErrorSeverity.LOW:
        return 'info';
      default:
        return 'error';
    }
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Get error statistics
   */
  getErrorStats(): {
    total: number;
    byType: Record<ErrorType, number>;
    bySeverity: Record<ErrorSeverity, number>;
    recent: StandardError[];
  } {
    const byType = {} as Record<ErrorType, number>;
    const bySeverity = {} as Record<ErrorSeverity, number>;

    this.errorLog.forEach(error => {
      byType[error.type] = (byType[error.type] || 0) + 1;
      bySeverity[error.severity] = (bySeverity[error.severity] || 0) + 1;
    });

    return {
      total: this.errorLog.length,
      byType,
      bySeverity,
      recent: this.errorLog.slice(-10)
    };
  }
}

// Export singleton instance
export const errorHandler = ErrorHandler.getInstance();

// Utility functions
export function createErrorResponse(
  type: ErrorType,
  code: string,
  message: string,
  details?: any,
  requestId?: string
): ErrorResponse {
  return errorHandler.createErrorResponse(type, code, message, details, requestId);
}

export function createSuccessResponse<T>(
  data: T,
  requestId?: string,
  pagination?: any
): SuccessResponse<T> {
  return errorHandler.createSuccessResponse(data, requestId, pagination);
}
