/**
 * Error Handler Unit Tests
 * Tests for centralized error handling functionality
 */

import { <PERSON>rrorHandler, ErrorType, ErrorSeverity, errorHandler } from '../error-handler';

describe('ErrorHandler', () => {
  let handler: ErrorHandler;

  beforeEach(() => {
    handler = ErrorHandler.getInstance();
  });

  describe('createError', () => {
    it('should create a standardized error with all required fields', () => {
      const error = handler.createError(
        ErrorType.WORKFLOW,
        'TEST_ERROR',
        'Test error message',
        { testContext: 'value' },
        ErrorSeverity.HIGH
      );

      expect(error).toMatchObject({
        type: ErrorType.WORKFLOW,
        severity: ErrorSeverity.HIGH,
        message: 'Test error message',
        code: 'TEST_ERROR',
        context: { testContext: 'value' },
        recoverable: true,
        retryable: false
      });

      expect(error.id).toBeDefined();
      expect(error.timestamp).toBeDefined();
      expect(error.correlationId).toBeDefined();
    });

    it('should set default severity to MEDIUM', () => {
      const error = handler.createError(
        ErrorType.SYSTEM,
        'DEFAULT_SEVERITY',
        'Test message'
      );

      expect(error.severity).toBe(ErrorSeverity.MEDIUM);
    });

    it('should determine recoverability based on error code', () => {
      const recoverableError = handler.createError(
        ErrorType.NETWORK,
        'NETWORK_ERROR',
        'Network error'
      );
      expect(recoverableError.recoverable).toBe(true);

      const nonRecoverableError = handler.createError(
        ErrorType.VALIDATION,
        'VALIDATION_ERROR',
        'Validation error'
      );
      expect(nonRecoverableError.recoverable).toBe(false);
    });

    it('should determine retryability based on error code', () => {
      const retryableError = handler.createError(
        ErrorType.NETWORK,
        'NETWORK_ERROR',
        'Network error'
      );
      expect(retryableError.retryable).toBe(true);

      const nonRetryableError = handler.createError(
        ErrorType.VALIDATION,
        'VALIDATION_ERROR',
        'Validation error'
      );
      expect(nonRetryableError.retryable).toBe(false);
    });
  });

  describe('createErrorResponse', () => {
    it('should create a standardized API error response', () => {
      const response = handler.createErrorResponse(
        ErrorType.WORKFLOW,
        'WORKFLOW_FAILED',
        'Workflow execution failed',
        { workflowId: '123' },
        'req-123'
      );

      expect(response).toMatchObject({
        success: false,
        error: {
          type: ErrorType.WORKFLOW,
          code: 'WORKFLOW_FAILED',
          message: 'Workflow execution failed',
          details: { workflowId: '123' }
        },
        meta: {
          requestId: 'req-123',
          version: '1.0'
        }
      });

      expect(response.error.correlationId).toBeDefined();
      expect(response.error.timestamp).toBeDefined();
    });
  });

  describe('createSuccessResponse', () => {
    it('should create a standardized API success response', () => {
      const data = { result: 'success' };
      const response = handler.createSuccessResponse(data, 'req-123');

      expect(response).toMatchObject({
        success: true,
        data: { result: 'success' },
        meta: {
          requestId: 'req-123',
          version: '1.0'
        }
      });
    });

    it('should include pagination metadata when provided', () => {
      const data = [1, 2, 3];
      const pagination = {
        page: 1,
        limit: 10,
        total: 3,
        hasNext: false,
        hasPrev: false
      };
      const response = handler.createSuccessResponse(data, 'req-123', pagination);

      expect(response.meta.pagination).toEqual(pagination);
    });
  });

  describe('handleError', () => {
    it('should handle Error objects correctly', () => {
      const originalError = new Error('Original error message');
      const standardError = handler.handleError(originalError, { operation: 'test' });

      expect(standardError.message).toBe('Original error message');
      expect(standardError.context.operation).toBe('test');
      expect(standardError.context.originalStack).toBeDefined();
    });

    it('should handle non-Error objects', () => {
      const standardError = handler.handleError('String error', { operation: 'test' });

      expect(standardError.message).toBe('String error');
      expect(standardError.type).toBe(ErrorType.SYSTEM);
      expect(standardError.code).toBe('UNKNOWN_ERROR');
    });

    it('should infer error type from message content', () => {
      const workflowError = new Error('Workflow execution failed');
      const standardError = handler.handleError(workflowError);

      expect(standardError.type).toBe(ErrorType.WORKFLOW);
    });

    it('should infer error code from message content', () => {
      const notFoundError = new Error('Resource not found');
      const standardError = handler.handleError(notFoundError);

      expect(standardError.code).toBe('NOT_FOUND');
    });
  });

  describe('executeWithRecovery', () => {
    it('should execute function successfully on first attempt', async () => {
      const mockFn = jest.fn().mockResolvedValue('success');
      
      const result = await handler.executeWithRecovery(mockFn);
      
      expect(result).toBe('success');
      expect(mockFn).toHaveBeenCalledTimes(1);
    });

    it('should retry on retryable errors', async () => {
      const mockFn = jest.fn()
        .mockRejectedValueOnce(new Error('Network error'))
        .mockRejectedValueOnce(new Error('Network error'))
        .mockResolvedValue('success');
      
      const result = await handler.executeWithRecovery(mockFn, {
        maxRetries: 3,
        retryDelayMs: 10,
        retryableErrors: ['NETWORK_ERROR']
      });
      
      expect(result).toBe('success');
      expect(mockFn).toHaveBeenCalledTimes(3);
    });

    it('should not retry on non-retryable errors', async () => {
      const mockFn = jest.fn().mockRejectedValue(new Error('Validation error'));
      
      await expect(handler.executeWithRecovery(mockFn, {
        maxRetries: 3,
        retryableErrors: ['NETWORK_ERROR']
      })).rejects.toThrow();
      
      expect(mockFn).toHaveBeenCalledTimes(1);
    });

    it('should use fallback action when all retries fail', async () => {
      const mockFn = jest.fn().mockRejectedValue(new Error('Network error'));
      const fallbackFn = jest.fn().mockResolvedValue('fallback result');
      
      const result = await handler.executeWithRecovery(mockFn, {
        maxRetries: 2,
        retryDelayMs: 10,
        retryableErrors: ['NETWORK_ERROR'],
        fallbackAction: fallbackFn
      });
      
      expect(result).toBe('fallback result');
      expect(mockFn).toHaveBeenCalledTimes(3); // Initial + 2 retries
      expect(fallbackFn).toHaveBeenCalledTimes(1);
    });

    it('should throw error when fallback also fails', async () => {
      const mockFn = jest.fn().mockRejectedValue(new Error('Network error'));
      const fallbackFn = jest.fn().mockRejectedValue(new Error('Fallback failed'));
      
      await expect(handler.executeWithRecovery(mockFn, {
        maxRetries: 1,
        retryDelayMs: 10,
        retryableErrors: ['NETWORK_ERROR'],
        fallbackAction: fallbackFn
      })).rejects.toThrow();
    });
  });

  describe('getErrorStats', () => {
    it('should return error statistics', () => {
      // Create some errors
      handler.createError(ErrorType.WORKFLOW, 'ERROR1', 'Message 1');
      handler.createError(ErrorType.AI, 'ERROR2', 'Message 2', {}, ErrorSeverity.HIGH);
      handler.createError(ErrorType.WORKFLOW, 'ERROR3', 'Message 3', {}, ErrorSeverity.LOW);

      const stats = handler.getErrorStats();

      expect(stats.total).toBe(3);
      expect(stats.byType[ErrorType.WORKFLOW]).toBe(2);
      expect(stats.byType[ErrorType.AI]).toBe(1);
      expect(stats.bySeverity[ErrorSeverity.MEDIUM]).toBe(1); // Default severity
      expect(stats.bySeverity[ErrorSeverity.HIGH]).toBe(1);
      expect(stats.bySeverity[ErrorSeverity.LOW]).toBe(1);
      expect(stats.recent).toHaveLength(3);
    });
  });

  describe('singleton behavior', () => {
    it('should return the same instance', () => {
      const instance1 = ErrorHandler.getInstance();
      const instance2 = ErrorHandler.getInstance();

      expect(instance1).toBe(instance2);
    });

    it('should maintain state across getInstance calls', () => {
      const instance1 = ErrorHandler.getInstance();
      instance1.createError(ErrorType.SYSTEM, 'TEST', 'Test message');

      const instance2 = ErrorHandler.getInstance();
      const stats = instance2.getErrorStats();

      expect(stats.total).toBeGreaterThan(0);
    });
  });

  describe('exported utility functions', () => {
    it('should use singleton instance for createErrorResponse', () => {
      const response = require('../error-handler').createErrorResponse(
        ErrorType.SYSTEM,
        'TEST_CODE',
        'Test message'
      );

      expect(response.success).toBe(false);
      expect(response.error.code).toBe('TEST_CODE');
    });

    it('should use singleton instance for createSuccessResponse', () => {
      const response = require('../error-handler').createSuccessResponse({ test: 'data' });

      expect(response.success).toBe(true);
      expect(response.data.test).toBe('data');
    });
  });
});

// Integration tests
describe('ErrorHandler Integration', () => {
  it('should work with real async operations', async () => {
    const handler = ErrorHandler.getInstance();

    // Simulate a function that fails twice then succeeds
    let attempts = 0;
    const unreliableFunction = async () => {
      attempts++;
      if (attempts < 3) {
        throw new Error('Temporary failure');
      }
      return 'success';
    };

    const result = await handler.executeWithRecovery(unreliableFunction, {
      maxRetries: 3,
      retryDelayMs: 1
    });

    expect(result).toBe('success');
    expect(attempts).toBe(3);
  });

  it('should handle complex error scenarios', async () => {
    const handler = ErrorHandler.getInstance();

    const complexOperation = async () => {
      // Simulate different types of errors
      const errorTypes = ['network', 'timeout', 'validation'];
      const randomError = errorTypes[Math.floor(Math.random() * errorTypes.length)];
      
      if (randomError === 'validation') {
        throw new Error('Validation failed');
      } else {
        throw new Error(`${randomError} error`);
      }
    };

    try {
      await handler.executeWithRecovery(complexOperation, {
        maxRetries: 2,
        retryDelayMs: 1
      });
    } catch (error) {
      // Should be a standardized error
      expect(error).toHaveProperty('type');
      expect(error).toHaveProperty('code');
      expect(error).toHaveProperty('message');
      expect(error).toHaveProperty('timestamp');
    }
  });
});
