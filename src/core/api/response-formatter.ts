/**
 * API Response Formatter
 * Standardizes API response formats across all endpoints
 */

import { NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid';
import { ErrorType, ErrorSeverity } from '../utils/error-handler';

// Standard API Response Types
export interface APISuccessResponse<T = any> {
  success: true;
  data: T;
  meta: {
    requestId: string;
    timestamp: string;
    version: string;
    pagination?: PaginationMeta;
  };
}

export interface APIErrorResponse {
  success: false;
  error: {
    type: ErrorType;
    code: string;
    message: string;
    details?: any;
    correlationId?: string;
  };
  meta: {
    requestId: string;
    timestamp: string;
    version: string;
  };
}

export interface PaginationMeta {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export type APIResponse<T = any> = APISuccessResponse<T> | APIErrorResponse;

// HTTP Status Code Mappings
export const ERROR_STATUS_CODES: Record<string, number> = {
  // Client Errors (4xx)
  'VALIDATION_ERROR': 400,
  'INVALID_REQUEST': 400,
  'MISSING_PARAMETER': 400,
  'INVALID_PARAMETER': 400,
  'AUTHENTICATION_FAILED': 401,
  'UNAUTHORIZED': 401,
  'AUTHORIZATION_DENIED': 403,
  'FORBIDDEN': 403,
  'NOT_FOUND': 404,
  'WORKFLOW_NOT_FOUND': 404,
  'EXECUTION_NOT_FOUND': 404,
  'REVIEW_NOT_FOUND': 404,
  'METHOD_NOT_ALLOWED': 405,
  'CONFLICT': 409,
  'REVIEW_ALREADY_COMPLETED': 409,
  'REVIEW_EXPIRED': 410,
  'PAYLOAD_TOO_LARGE': 413,
  'UNSUPPORTED_MEDIA_TYPE': 415,
  'RATE_LIMIT_EXCEEDED': 429,
  'RATE_LIMIT_REQUESTS': 429,
  'RATE_LIMIT_TOKENS': 429,

  // Server Errors (5xx)
  'INTERNAL_ERROR': 500,
  'UNKNOWN_ERROR': 500,
  'SYSTEM_ERROR': 500,
  'AI_PROVIDER_ERROR': 500,
  'WORKFLOW_EXECUTION_FAILED': 500,
  'SERVICE_UNAVAILABLE': 503,
  'REDIS_CONNECTION_FAILED': 503,
  'DATABASE_ERROR': 503,
  'TIMEOUT': 504,
  'AI_TIMEOUT': 504
};

export class ResponseFormatter {
  private static readonly API_VERSION = '1.0';

  /**
   * Create a standardized success response
   */
  static success<T>(
    data: T,
    requestId?: string,
    pagination?: PaginationMeta
  ): NextResponse<APISuccessResponse<T>> {
    const response: APISuccessResponse<T> = {
      success: true,
      data,
      meta: {
        requestId: requestId || uuidv4(),
        timestamp: new Date().toISOString(),
        version: ResponseFormatter.API_VERSION,
        ...(pagination && { pagination })
      }
    };

    return NextResponse.json(response);
  }

  /**
   * Create a standardized error response
   */
  static error(
    type: ErrorType,
    code: string,
    message: string,
    details?: any,
    requestId?: string,
    correlationId?: string
  ): NextResponse<APIErrorResponse> {
    const statusCode = ERROR_STATUS_CODES[code] || 500;

    const response: APIErrorResponse = {
      success: false,
      error: {
        type,
        code,
        message,
        ...(details && { details }),
        ...(correlationId && { correlationId })
      },
      meta: {
        requestId: requestId || uuidv4(),
        timestamp: new Date().toISOString(),
        version: ResponseFormatter.API_VERSION
      }
    };

    return NextResponse.json(response, { status: statusCode });
  }

  /**
   * Create a validation error response
   */
  static validationError(
    message: string,
    validationErrors: Array<{ field: string; message: string; code?: string }>,
    requestId?: string
  ): NextResponse<APIErrorResponse> {
    return ResponseFormatter.error(
      ErrorType.VALIDATION,
      'VALIDATION_ERROR',
      message,
      { validationErrors },
      requestId
    );
  }

  /**
   * Create a not found error response
   */
  static notFound(
    resource: string,
    id: string,
    requestId?: string
  ): NextResponse<APIErrorResponse> {
    return ResponseFormatter.error(
      ErrorType.SYSTEM,
      'NOT_FOUND',
      `${resource} with id '${id}' not found`,
      { resource, id },
      requestId
    );
  }

  /**
   * Create an unauthorized error response
   */
  static unauthorized(
    message: string = 'Authentication required',
    requestId?: string
  ): NextResponse<APIErrorResponse> {
    return ResponseFormatter.error(
      ErrorType.AUTHENTICATION,
      'AUTHENTICATION_FAILED',
      message,
      undefined,
      requestId
    );
  }

  /**
   * Create a forbidden error response
   */
  static forbidden(
    message: string = 'Access denied',
    requestId?: string
  ): NextResponse<APIErrorResponse> {
    return ResponseFormatter.error(
      ErrorType.AUTHORIZATION,
      'AUTHORIZATION_DENIED',
      message,
      undefined,
      requestId
    );
  }

  /**
   * Create a rate limit error response
   */
  static rateLimit(
    message: string = 'Rate limit exceeded',
    retryAfter?: number,
    requestId?: string
  ): NextResponse<APIErrorResponse> {
    const response = ResponseFormatter.error(
      ErrorType.SYSTEM,
      'RATE_LIMIT_EXCEEDED',
      message,
      { retryAfter },
      requestId
    );

    if (retryAfter) {
      response.headers.set('Retry-After', retryAfter.toString());
    }

    return response;
  }

  /**
   * Create a paginated success response
   */
  static paginated<T>(
    data: T[],
    page: number,
    limit: number,
    total: number,
    requestId?: string
  ): NextResponse<APISuccessResponse<T[]>> {
    const totalPages = Math.ceil(total / limit);
    const pagination: PaginationMeta = {
      page,
      limit,
      total,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1
    };

    return ResponseFormatter.success(data, requestId, pagination);
  }

  /**
   * Create a response from a standard error object
   */
  static fromError(
    error: any,
    requestId?: string
  ): NextResponse<APIErrorResponse> {
    // Handle standardized errors
    if (error.type && error.code && error.message) {
      return ResponseFormatter.error(
        error.type,
        error.code,
        error.message,
        error.context,
        requestId,
        error.correlationId
      );
    }

    // Handle generic errors
    const message = error instanceof Error ? error.message : String(error);
    return ResponseFormatter.error(
      ErrorType.SYSTEM,
      'INTERNAL_ERROR',
      message,
      undefined,
      requestId
    );
  }

  /**
   * Wrap async handler with standardized error handling
   */
  static wrapHandler<T>(
    handler: (request: Request, context?: any) => Promise<NextResponse<APISuccessResponse<T>>>
  ) {
    return async (request: Request, context?: any): Promise<NextResponse<APIResponse<T>>> => {
      const requestId = uuidv4();
      
      try {
        return await handler(request, context);
      } catch (error) {
        console.error(`API Error [${requestId}]:`, error);
        return ResponseFormatter.fromError(error, requestId);
      }
    };
  }

  /**
   * Extract request ID from headers or generate new one
   */
  static getRequestId(request: Request): string {
    return request.headers.get('x-request-id') || uuidv4();
  }

  /**
   * Add CORS headers to response
   */
  static addCorsHeaders(response: NextResponse): NextResponse {
    response.headers.set('Access-Control-Allow-Origin', '*');
    response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, x-request-id');
    return response;
  }

  /**
   * Create an OPTIONS response for CORS preflight
   */
  static options(): NextResponse {
    const response = new NextResponse(null, { status: 200 });
    return ResponseFormatter.addCorsHeaders(response);
  }
}

// Utility functions for common response patterns
export function successResponse<T>(
  data: T,
  requestId?: string,
  pagination?: PaginationMeta
): NextResponse<APISuccessResponse<T>> {
  return ResponseFormatter.success(data, requestId, pagination);
}

export function errorResponse(
  type: ErrorType,
  code: string,
  message: string,
  details?: any,
  requestId?: string
): NextResponse<APIErrorResponse> {
  return ResponseFormatter.error(type, code, message, details, requestId);
}

export function validationErrorResponse(
  message: string,
  validationErrors: Array<{ field: string; message: string; code?: string }>,
  requestId?: string
): NextResponse<APIErrorResponse> {
  return ResponseFormatter.validationError(message, validationErrors, requestId);
}

export function notFoundResponse(
  resource: string,
  id: string,
  requestId?: string
): NextResponse<APIErrorResponse> {
  return ResponseFormatter.notFound(resource, id, requestId);
}

export function paginatedResponse<T>(
  data: T[],
  page: number,
  limit: number,
  total: number,
  requestId?: string
): NextResponse<APISuccessResponse<T[]>> {
  return ResponseFormatter.paginated(data, page, limit, total, requestId);
}

// Export the main formatter class
export { ResponseFormatter };
