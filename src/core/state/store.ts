/**
 * Simplified State Store Implementation
 * Flat state structure with basic operations
 */

import { v4 as uuidv4 } from 'uuid';
import {
  SimplifiedState,
  ISimplifiedStateStore,
  IStorageAdapter,
  MemoryStorageAdapter,
  SystemEvent,
  SystemError,
  UsageStats,
  ContentItem,
  Review,
  EssentialEventType
} from './types';
import { Workflow, WorkflowExecution } from '../workflow/types';

export class SimplifiedStateStore implements ISimplifiedStateStore {
  private static readonly STATE_KEY = 'system_state';

  constructor(private storage: IStorageAdapter = new MemoryStorageAdapter()) {}

  // Basic state operations
  async get(): Promise<SimplifiedState | null> {
    return await this.storage.get(SimplifiedStateStore.STATE_KEY);
  }

  async set(state: SimplifiedState): Promise<void> {
    state.lastUpdated = new Date().toISOString();
    state.version += 1;
    await this.storage.set(SimplifiedStateStore.STATE_KEY, state);
  }

  async update(updateFn: (state: SimplifiedState | null) => SimplifiedState | null): Promise<void> {
    const currentState = await this.get();
    const newState = updateFn(currentState);

    if (newState) {
      await this.set(newState);
    }
  }

  // Initialize empty state
  async initialize(): Promise<void> {
    const existingState = await this.get();
    if (!existingState) {
      const initialState: SimplifiedState = {
        workflows: {},
        executions: {},
        content: {},
        reviews: {},
        approvalGates: {},
        system: {
          events: [],
          errors: [],
          usage: {
            totalWorkflows: 0,
            totalExecutions: 0,
            totalContent: 0,
            totalReviews: 0,
            aiUsage: {
              totalRequests: 0,
              totalTokens: 0,
              totalCost: 0,
              byProvider: {}
            },
            lastReset: new Date().toISOString()
          },
          config: {
            defaultAIProvider: 'openai',
            defaultAIModel: 'gpt-4',
            defaultReviewTimeout: 24,
            emailNotifications: true,
            rateLimits: {
              workflowsPerHour: 100,
              aiRequestsPerMinute: 60
            }
          }
        },
        lastUpdated: new Date().toISOString(),
        version: 1
      };

      await this.set(initialState);
    }
  }

  // Workflow operations
  async getWorkflow(id: string): Promise<Workflow | null> {
    const state = await this.get();
    return state?.workflows[id] || null;
  }

  async setWorkflow(workflow: Workflow): Promise<void> {
    await this.update(state => {
      if (!state) return null;

      return {
        ...state,
        workflows: {
          ...state.workflows,
          [workflow.id]: workflow
        },
        system: {
          ...state.system,
          usage: {
            ...state.system.usage,
            totalWorkflows: Object.keys(state.workflows).length + 1
          }
        }
      };
    });
  }

  // Execution operations
  async getExecution(id: string): Promise<WorkflowExecution | null> {
    const state = await this.get();
    if (!state || !state.executions) {
      console.log(`⚠️ No state or executions found for execution ${id}`);
      return null;
    }
    return state.executions[id] || null;
  }

  async setExecution(execution: WorkflowExecution): Promise<void> {
    await this.update(state => {
      if (!state) return null;

      const isNew = !state.executions[execution.id];

      return {
        ...state,
        executions: {
          ...state.executions,
          [execution.id]: execution
        },
        system: {
          ...state.system,
          usage: {
            ...state.system.usage,
            totalExecutions: isNew ? state.system.usage.totalExecutions + 1 : state.system.usage.totalExecutions
          }
        }
      };
    });

    // Emit event for execution status changes
    if (execution.status === 'running') {
      await this.addEvent({
        type: EssentialEventType.WORKFLOW_STARTED,
        data: { executionId: execution.id, workflowId: execution.workflowId }
      });
    } else if (execution.status === 'completed') {
      await this.addEvent({
        type: EssentialEventType.WORKFLOW_COMPLETED,
        data: { executionId: execution.id, workflowId: execution.workflowId }
      });
    } else if (execution.status === 'failed') {
      await this.addEvent({
        type: EssentialEventType.WORKFLOW_FAILED,
        data: { executionId: execution.id, workflowId: execution.workflowId, error: execution.error }
      });
    }
  }

  // Content operations
  async getContent(id: string): Promise<ContentItem | null> {
    const state = await this.get();
    return state?.content[id] || null;
  }

  async setContent(content: ContentItem): Promise<void> {
    await this.update(state => {
      if (!state) return null;

      const isNew = !state.content[content.id];

      return {
        ...state,
        content: {
          ...state.content,
          [content.id]: content
        },
        system: {
          ...state.system,
          usage: {
            ...state.system.usage,
            totalContent: isNew ? state.system.usage.totalContent + 1 : state.system.usage.totalContent
          }
        }
      };
    });

    // Emit event for content ready
    if (content.status === 'approved') {
      await this.addEvent({
        type: EssentialEventType.CONTENT_READY,
        data: { contentId: content.id, type: content.type }
      });
    }
  }

  // Review operations
  async getReview(id: string): Promise<Review | null> {
    const state = await this.get();
    return state?.reviews[id] || null;
  }

  async setReview(review: Review): Promise<void> {
    await this.update(state => {
      if (!state) return null;

      const isNew = !state.reviews[review.id];

      return {
        ...state,
        reviews: {
          ...state.reviews,
          [review.id]: review
        },
        system: {
          ...state.system,
          usage: {
            ...state.system.usage,
            totalReviews: isNew ? state.system.usage.totalReviews + 1 : state.system.usage.totalReviews
          }
        }
      };
    });

    // Emit events for review status changes
    if (review.status === 'pending') {
      await this.addEvent({
        type: EssentialEventType.REVIEW_NEEDED,
        data: { reviewId: review.id, contentId: review.contentId, type: review.type }
      });
    } else if (review.status === 'completed') {
      await this.addEvent({
        type: EssentialEventType.REVIEW_COMPLETE,
        data: { reviewId: review.id, contentId: review.contentId, decision: review.decision }
      });
    }
  }

  // Event operations
  async addEvent(event: Omit<SystemEvent, 'id' | 'timestamp'>): Promise<void> {
    const systemEvent: SystemEvent = {
      id: uuidv4(),
      timestamp: new Date().toISOString(),
      ...event
    };

    await this.update(state => {
      if (!state) return null;

      // Keep only last 1000 events to prevent memory issues
      const events = [...state.system.events, systemEvent].slice(-1000);

      return {
        ...state,
        system: {
          ...state.system,
          events
        }
      };
    });
  }

  async getEvents(limit: number = 100): Promise<SystemEvent[]> {
    const state = await this.get();
    if (!state) return [];

    return state.system.events
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
      .slice(0, limit);
  }

  // Error operations
  async addError(error: Omit<SystemError, 'id' | 'timestamp'>): Promise<void> {
    const systemError: SystemError = {
      id: uuidv4(),
      timestamp: new Date().toISOString(),
      resolved: false,
      ...error
    };

    await this.update(state => {
      if (!state) return null;

      // Keep only last 500 errors
      const errors = [...state.system.errors, systemError].slice(-500);

      return {
        ...state,
        system: {
          ...state.system,
          errors
        }
      };
    });
  }

  async getErrors(resolved?: boolean): Promise<SystemError[]> {
    const state = await this.get();
    if (!state) return [];

    let errors = state.system.errors;

    if (resolved !== undefined) {
      errors = errors.filter(error => error.resolved === resolved);
    }

    return errors.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
  }

  async resolveError(id: string): Promise<void> {
    await this.update(state => {
      if (!state) return null;

      const errors = state.system.errors.map(error =>
        error.id === id ? { ...error, resolved: true } : error
      );

      return {
        ...state,
        system: {
          ...state.system,
          errors
        }
      };
    });
  }

  // Usage tracking
  async updateUsage(updates: Partial<UsageStats>): Promise<void> {
    await this.update(state => {
      if (!state) return null;

      return {
        ...state,
        system: {
          ...state.system,
          usage: {
            ...state.system.usage,
            ...updates
          }
        }
      };
    });
  }

  async getUsage(): Promise<UsageStats> {
    const state = await this.get();
    return state?.system.usage || {
      totalWorkflows: 0,
      totalExecutions: 0,
      totalContent: 0,
      totalReviews: 0,
      aiUsage: {
        totalRequests: 0,
        totalTokens: 0,
        totalCost: 0,
        byProvider: {}
      },
      lastReset: new Date().toISOString()
    };
  }

  // Utility methods
  async getAllWorkflows(): Promise<Workflow[]> {
    const state = await this.get();
    return state ? Object.values(state.workflows) : [];
  }

  async getAllExecutions(): Promise<WorkflowExecution[]> {
    const state = await this.get();
    return state ? Object.values(state.executions) : [];
  }

  async deleteExecution(id: string): Promise<boolean> {
    let deleted = false;

    await this.update(state => {
      if (!state || !state.executions[id]) {
        return state;
      }

      deleted = true;
      const { [id]: removedExecution, ...remainingExecutions } = state.executions;

      return {
        ...state,
        executions: remainingExecutions,
        system: {
          ...state.system,
          usage: {
            ...state.system.usage,
            totalExecutions: Math.max(0, state.system.usage.totalExecutions - 1)
          }
        }
      };
    });

    return deleted;
  }

  async getExecutionsByStatus(status: string): Promise<WorkflowExecution[]> {
    const executions = await this.getAllExecutions();
    return executions.filter(execution => execution.status === status);
  }

  async getContentByStatus(status: string): Promise<ContentItem[]> {
    const state = await this.get();
    if (!state) return [];

    return Object.values(state.content).filter(content => content.status === status);
  }

  async getPendingReviews(): Promise<Review[]> {
    const state = await this.get();
    if (!state) return [];

    return Object.values(state.reviews).filter(review => review.status === 'pending');
  }

  // Workflow layout management
  async updateWorkflowLayout(workflowId: string, layout: { nodes: any[]; edges: any[] }): Promise<void> {
    await this.update(state => {
      if (!state || !state.executions[workflowId]) {
        return state;
      }

      return {
        ...state,
        executions: {
          ...state.executions,
          [workflowId]: {
            ...state.executions[workflowId],
            layout,
            updatedAt: new Date().toISOString()
          }
        }
      };
    });
  }

  async updateStepStatus(workflowId: string, stepId: string, status: string, progress?: number): Promise<void> {
    await this.update(state => {
      if (!state || !state.executions[workflowId]) {
        return state;
      }

      const execution = state.executions[workflowId];
      const steps = execution.steps || [];

      const updatedSteps = steps.map(step =>
        step.id === stepId
          ? { ...step, status, progress, updatedAt: new Date().toISOString() }
          : step
      );

      return {
        ...state,
        executions: {
          ...state.executions,
          [workflowId]: {
            ...execution,
            steps: updatedSteps,
            currentStep: status === 'running' ? stepId : execution.currentStep,
            updatedAt: new Date().toISOString()
          }
        }
      };
    });

    // Add event for step status change
    await this.addEvent({
      type: status === 'completed' ? EssentialEventType.WORKFLOW_COMPLETED :
            status === 'failed' ? EssentialEventType.WORKFLOW_FAILED :
            EssentialEventType.WORKFLOW_STARTED,
      data: { workflowId, stepId, status, progress }
    });
  }
}
