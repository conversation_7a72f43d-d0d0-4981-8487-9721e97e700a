/**
 * Singleton instances for workflow system
 * Ensures all API routes use the same instances
 */

import { WorkflowEngine } from './engine';
import { SimplifiedStateStore } from '../state/store';
import { RedisStorageAdapter } from '../state/redis-adapter';
import { AIModelManager } from '../ai/model-manager';
import { SimplifiedReviewSystem } from '../review/system';
import { TemplateRegistry } from './templates';
import { EnhancedTemplateRegistry } from './template-processor';

// Create singleton instances
let stateStoreInstance: SimplifiedStateStore | null = null;
let aiManagerInstance: AIModelManager | null = null;
let reviewSystemInstance: SimplifiedReviewSystem | null = null;
let workflowEngineInstance: WorkflowEngine | null = null;
let templateRegistryInstance: TemplateRegistry | null = null;
let enhancedTemplateRegistryInstance: EnhancedTemplateRegistry | null = null;

export function getStateStore(): SimplifiedStateStore {
  if (!stateStoreInstance) {
    // Use Redis adapter for persistent storage across serverless functions
    const redisAdapter = new RedisStorageAdapter('workflow:', 60 * 60 * 24 * 7); // 7 days TTL
    stateStoreInstance = new SimplifiedStateStore(redisAdapter);
    stateStoreInstance.initialize().catch(console.error);
  }
  return stateStoreInstance;
}

export function getAIManager(): AIModelManager {
  if (!aiManagerInstance) {
    aiManagerInstance = new AIModelManager({
      defaultProvider: 'openai',
      defaultModel: 'gpt-4',
      providers: {
        openai: {
          apiKey: process.env.OPENAI_API_KEY || ''
        }
      },
      costTracking: true,
      rateLimiting: {
        enabled: false,
        requestsPerMinute: 60,
        tokensPerMinute: 100000
      }
    });
  }
  return aiManagerInstance;
}

export function getReviewSystem(): SimplifiedReviewSystem {
  if (!reviewSystemInstance) {
    reviewSystemInstance = new SimplifiedReviewSystem(getStateStore());
  }
  return reviewSystemInstance;
}

export function getWorkflowEngine(): WorkflowEngine {
  if (!workflowEngineInstance) {
    workflowEngineInstance = new WorkflowEngine(
      getStateStore(),
      getAIManager(),
      getReviewSystem()
    );
  }
  return workflowEngineInstance;
}

export function getTemplateRegistry(): TemplateRegistry {
  if (!templateRegistryInstance) {
    templateRegistryInstance = new TemplateRegistry();
  }
  return templateRegistryInstance;
}

export function getEnhancedTemplateRegistry(): EnhancedTemplateRegistry {
  if (!enhancedTemplateRegistryInstance) {
    enhancedTemplateRegistryInstance = new EnhancedTemplateRegistry();

    // Register essential templates
    const { ESSENTIAL_TEMPLATES } = require('./templates');
    ESSENTIAL_TEMPLATES.forEach((template: any) => {
      try {
        enhancedTemplateRegistryInstance!.registerTemplate(template);
      } catch (error) {
        console.error(`Failed to register template ${template.id}:`, error);
      }
    });

    console.log(`📋 Enhanced template registry initialized with ${ESSENTIAL_TEMPLATES.length} templates`);
  }
  return enhancedTemplateRegistryInstance;
}
